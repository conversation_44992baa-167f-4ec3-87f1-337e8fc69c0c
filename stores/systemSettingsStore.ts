import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient, apiEndpoints, apiRequest } from '@/lib/api';
import { SystemSetting, SystemSettingsResponse, SystemSettingsState } from '@/types/api';

interface SystemSettingsStore extends SystemSettingsState {
  fetchSystemSettings: () => Promise<void>;
  refreshSystemSettings: () => Promise<void>;
  getSettingValue: (key: string) => string | undefined;
  isLoading: () => boolean;
  hasError: () => boolean;
  clearError: () => void;
}

export const useSystemSettingsStore = create<SystemSettingsStore>()(
  persist(
    (set, get) => ({
      // Initial state
      settings: [],
      loading: false,
      error: null,
      lastFetch: null,

      // Fetch system settings from API
      fetchSystemSettings: async () => {
        const state = get();
        
        // Avoid duplicate requests if already loading
        if (state.loading) return;

        // Check if we have recent data (cache for 5 minutes)
        const now = Date.now();
        const cacheTime = 5 * 60 * 1000; // 5 minutes
        if (state.lastFetch && (now - state.lastFetch) < cacheTime && state.settings.length > 0) {
          return;
        }

        set({ loading: true, error: null });

        try {
          const response = await apiRequest<SystemSettingsResponse>(() =>
            apiClient.get(`${apiEndpoints.systemSettings}?visible_only=1`)
          );

          set({
            settings: response.data,
            loading: false,
            error: null,
            lastFetch: now,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch system settings';
          set({
            loading: false,
            error: errorMessage,
          });
          console.error('Error fetching system settings:', error);
        }
      },

      // Force refresh system settings (ignores cache)
      refreshSystemSettings: async () => {
        set({ lastFetch: null });
        await get().fetchSystemSettings();
      },

      // Get setting by key
      getSettingByKey: (key: string) => {
        return get().settings.find(setting => setting.key === key);
      },

      // Get setting value by key
      getSettingValue: (key: string) => {
        const setting = get().getSettingByKey(key);
        return setting?.value;
      },

      // Helper methods
      isLoading: () => get().loading,
      hasError: () => !!get().error,
      clearError: () => set({ error: null }),
    }),
    {
      name: 'system-settings-storage',
      // Only persist the settings data, not loading states
      partialize: (state) => ({
        settings: state.settings,
        lastFetch: state.lastFetch,
      }),
      // Don't auto-fetch on rehydration - only fetch when explicitly called
      onRehydrateStorage: () => (state) => {
        // Just rehydrate the state, don't auto-fetch
        return state;
      },
    }
  )
);

// Selector hooks for common use cases
export const useSystemSetting = (key: string) => {
  return useSystemSettingsStore((state) => state.getSettingValue(key));
};

export const useSystemSettingsLoading = () => {
  return useSystemSettingsStore((state) => state.loading);
};

export const useSystemSettingsError = () => {
  return useSystemSettingsStore((state) => state.error);
};
