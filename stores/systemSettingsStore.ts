import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient, apiEndpoints, apiRequest } from '@/lib/api';
import { SystemSetting, SystemSettingsResponse, SystemSettingsState } from '@/types/api';

interface SystemSettingsStore extends SystemSettingsState {
  fetchSystemSettings: (params?: { key_filter?: string }) => Promise<void>;
  refreshSystemSettings: () => Promise<void>;
  getSettingByKey: (key: string) => SystemSetting | undefined | any;
  getSettingValue: (key: string) => string | undefined | any;
  getSettingsByKeyPattern: (pattern: string) => SystemSetting[];
  getBannerSettings: () => SystemSetting[];
  getMenuSettings: () => SystemSetting[];
  isLoading: () => boolean;
  hasError: () => boolean;
  clearError: () => void;
}

export const useSystemSettingsStore = create<SystemSettingsStore>()(
  persist(
    (set, get) => ({
      // Initial state
      settings: [],
      loading: false,
      error: null,
      lastFetch: null,

      // Fetch system settings from API
      fetchSystemSettings: async (params?: { key_filter?: string }) => {
        const state = get();

        // Avoid duplicate requests if already loading
        if (state.loading) return;

        // Check if we have recent data (cache for 5 minutes)
        const now = Date.now();
        const cacheTime = 5 * 60 * 1000; // 5 minutes
        if (state.lastFetch && (now - state.lastFetch) < cacheTime && state.settings.length > 0 && !params?.key_filter) {
          return;
        }

        set({ loading: true, error: null });

        try {
          const queryParams = new URLSearchParams({ visible_only: '1' });
          if (params?.key_filter) {
            queryParams.append('key_filter', params.key_filter);
          }

          const response = await apiRequest<SystemSettingsResponse>(() =>
            apiClient.get(`${apiEndpoints.systemSettings}?${queryParams.toString()}`)
          );

          set({
            settings: response.data,
            loading: false,
            error: null,
            lastFetch: now,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch system settings';
          set({
            loading: false,
            error: errorMessage,
          });
          console.error('Error fetching system settings:', error);
        }
      },

      // Force refresh system settings (ignores cache)
      refreshSystemSettings: async () => {
        set({ lastFetch: null });
        await get().fetchSystemSettings();
      },

      // Get setting by key
      getSettingByKey: (key: string) => {
        return get().settings.find(setting => setting.key === key);
      },

      // Get setting value by key
      getSettingValue: (key: string) => {
        const setting = get().getSettingByKey(key);
        return setting?.value;
      },

      // Get settings by key pattern (e.g., "HERO_BANNER_", "PROMOTION_")
      getSettingsByKeyPattern: (pattern: string) => {
        return get().settings.filter(setting => setting.key.startsWith(pattern));
      },

      // Get all banner-related settings
      getBannerSettings: () => {
        const bannerPatterns = ['HERO_BANNER_', 'PROMOTION_BANNER_', 'SHOP_NOW_'];
        return get().settings.filter(setting =>
          bannerPatterns.some(pattern => setting.key.startsWith(pattern))
        );
      },

      // Get menu-related settings
      getMenuSettings: () => {
        return get().settings.filter(setting => setting.key.startsWith('HEAD_SECTION_MENU_'));
      },

      // Helper methods
      isLoading: () => get().loading,
      hasError: () => !!get().error,
      clearError: () => set({ error: null }),
    }),
    {
      name: 'system-settings-storage',
      // Only persist the settings data, not loading states
      partialize: (state) => ({
        settings: state.settings,
        lastFetch: state.lastFetch,
      }),
      // Don't auto-fetch on rehydration - only fetch when explicitly called
      onRehydrateStorage: () => (state) => {
        // Just rehydrate the state, don't auto-fetch
        return state;
      },
    }
  )
);

// Selector hooks for common use cases
export const useSystemSetting = (key: string) => {
  return useSystemSettingsStore((state) => state.getSettingValue(key));
};

export const useSystemSettingsLoading = () => {
  return useSystemSettingsStore((state) => state.loading);
};

export const useSystemSettingsError = () => {
  return useSystemSettingsStore((state) => state.error);
};
