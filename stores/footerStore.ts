import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient, apiEndpoints, apiRequest } from '@/lib/api';
import { FooterData, FooterResponse, FooterState } from '@/types/api';

interface FooterStore extends FooterState {
  fetchFooter: () => Promise<void>;
  refreshFooter: () => Promise<void>;
  isLoading: () => boolean;
  hasError: () => boolean;
  clearError: () => void;
}

export const useFooterStore = create<FooterStore>()(
  persist(
    (set, get) => ({
      // Initial state
      footer: null,
      loading: false,
      error: null,
      lastFetch: null,

      // Fetch footer data from API
      fetchFooter: async () => {
        const state = get();
        
        // Avoid duplicate requests if already loading
        if (state.loading) return;

        // Check if we have recent data (cache for 10 minutes)
        // const now = Date.now();
        // const cacheTime = 10 * 60 * 1000; // 10 minutes
        // if (state.lastFetch && (now - state.lastFetch) < cacheTime && state.footer) {
        //   return;
        // }

        set({ loading: true, error: null });

        try {
          const response = await apiRequest<FooterResponse>(() =>
            apiClient.get(apiEndpoints.footer)
          );

          set({
            footer: response.data,
            loading: false,
            error: null,
            // lastFetch: now,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch footer data';
          set({
            loading: false,
            error: errorMessage,
          });
          console.error('Error fetching footer data:', error);
        }
      },

      // Force refresh footer data (ignores cache)
      refreshFooter: async () => {
        set({ lastFetch: null });
        await get().fetchFooter();
      },

      // Helper methods
      isLoading: () => get().loading,
      hasError: () => !!get().error,
      clearError: () => set({ error: null }),
    }),
    {
      name: 'footer-storage',
      // Only persist the footer data, not loading states
      partialize: (state) => ({
        footer: state.footer,
        lastFetch: state.lastFetch,
      }),
      // Don't auto-fetch on rehydration - only fetch when explicitly called
      onRehydrateStorage: () => (state) => {
        // Just rehydrate the state, don't auto-fetch
        return state;
      },
    }
  )
);

// Selector hooks for common use cases
export const useFooterData = () => {
  return useFooterStore((state) => state.footer);
};

export const useFooterLoading = () => {
  return useFooterStore((state) => state.loading);
};

export const useFooterError = () => {
  return useFooterStore((state) => state.error);
};

export const useFooterSections = () => {
  return useFooterStore((state) => state.footer?.sections || []);
};

export const useFooterSocialLinks = () => {
  return useFooterStore((state) => state.footer?.social_links || {});
};
