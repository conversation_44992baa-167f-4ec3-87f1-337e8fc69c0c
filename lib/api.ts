import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { useLanguageStore } from '@/stores/languageStore';

// Create axios instance with base configuration
const createApiClient = (): AxiosInstance => {
  const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL;
  
  if (!baseURL) {
    throw new Error('NEXT_PUBLIC_API_BASE_URL is not defined in environment variables');
  }

  const client = axios.create({
    baseURL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  });

  // Request interceptor to add auth token and language
  client.interceptors.request.use(
    (config) => {
      // Get current language from store
      const language = useLanguageStore.getState().language;
      
      // Add language header
      config.headers['Accept-Language'] = language;
      config.headers['X-Locale'] = language;

      // Add auth token if available (from localStorage, cookies, etc.)
      const token = getAuthToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // Log request in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
          headers: config.headers,
          params: config.params,
          data: config.data,
        });
      }

      return config;
    },
    (error) => {
      console.error('❌ Request interceptor error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor for error handling
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      // Log response in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
          status: response.status,
          data: response.data,
        });
      }
      return response;
    },
    (error) => {
      // Log error in development
      if (process.env.NODE_ENV === 'development') {
        console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });
      }

      // Handle specific error cases
      if (error.response?.status === 401) {
        // Handle unauthorized - clear token and redirect to login
        clearAuthToken();
        // You can add redirect logic here if needed
      }

      return Promise.reject(error);
    }
  );

  return client;
};

// Helper functions for token management
const getAuthToken = (): string | null => {
  if (typeof window === 'undefined') return null;
  
  // Try to get token from localStorage first
  const token = localStorage.getItem('auth_token');
  if (token) return token;
  
  // Fallback to cookies if needed
  return getCookieValue('auth_token');
};

const setAuthToken = (token: string): void => {
  if (typeof window === 'undefined') return;
  
  localStorage.setItem('auth_token', token);
  // Also set as cookie if needed
  document.cookie = `auth_token=${token}; path=/; secure; samesite=strict`;
};

const clearAuthToken = (): void => {
  if (typeof window === 'undefined') return;
  
  localStorage.removeItem('auth_token');
  document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
};

const getCookieValue = (name: string): string | null => {
  if (typeof document === 'undefined') return null;
  
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null;
  }
  return null;
};

// Create the main API client instance
export const apiClient = createApiClient();

// Export helper functions
export { setAuthToken, clearAuthToken, getAuthToken };

// API utility functions
export const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
};

export const isNetworkError = (error: any): boolean => {
  return !error.response && error.request;
};

// Generic API request wrapper with error handling
export const apiRequest = async <T>(
  requestFn: () => Promise<AxiosResponse<T>>
): Promise<T> => {
  try {
    const response = await requestFn();
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

// Specific API endpoints
export const apiEndpoints = {
  systemSettings: '/api/v1/system-settings',
  footer: '/api/v1/footer',
  services: '/api/v1/services',
  categories: '/api/v1/categories',
  blogs: '/api/v1/blogs',
  blogDetail: (slug: string) => `/api/v1/blogs/${slug}`,
} as const;
