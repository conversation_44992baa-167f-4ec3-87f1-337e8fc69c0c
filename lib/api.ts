import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { useLanguageStore } from '@/stores/languageStore';

// Create axios instance with base configuration
const createApiClient = (): AxiosInstance => {
  const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL;

  if (!baseURL) {
    throw new Error('NEXT_PUBLIC_API_BASE_URL is not defined in environment variables');
  }

  const client = axios.create({
    baseURL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  });

  // Request interceptor to add auth token and language
  client.interceptors.request.use(
    (config) => {
      // Get current language from store
      const language = useLanguageStore.getState().language;

      // Add language header
      config.headers['Accept-Language'] = language;
      config.headers['X-Locale'] = language;

      // Add auth token if available (from localStorage, cookies, etc.)
      const token = getAuthToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }



      return config;
    },
    (error) => {
      console.error('❌ Request interceptor error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor for error handling
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      // Log response in development
      return response;
    },
    (error) => {
      // Log error in development

      // Handle specific error cases
      if (error.response?.status === 401) {
        // Handle unauthorized - clear token and redirect to login
        clearAuthToken();
        // You can add redirect logic here if needed
      }

      return Promise.reject(error);
    }
  );

  return client;
};

// Helper functions for token management
const getAuthToken = (): string | null => {
  if (typeof window === 'undefined') return null;

  // Try to get token from localStorage first
  const token = localStorage.getItem('auth_token');
  if (token) return token;

  // Fallback to cookies if needed
  return getCookieValue('auth_token');
};

const setAuthToken = (token: string): void => {
  if (typeof window === 'undefined') return;

  localStorage.setItem('auth_token', token);
  // Also set as cookie if needed
  document.cookie = `auth_token=${token}; path=/; secure; samesite=strict`;
};

const clearAuthToken = (): void => {
  if (typeof window === 'undefined') return;

  localStorage.removeItem('auth_token');
  document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
};

const getCookieValue = (name: string): string | null => {
  if (typeof document === 'undefined') return null;

  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null;
  }
  return null;
};

// Create the main API client instance
export const apiClient = createApiClient();

// Export helper functions
export { setAuthToken, clearAuthToken, getAuthToken };

// API utility functions
export const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
};

export const isNetworkError = (error: any): boolean => {
  return !error.response && error.request;
};

// Generic API request wrapper with error handling
export const apiRequest = async <T>(
  requestFn: () => Promise<AxiosResponse<T>>
): Promise<T> => {
  try {
    const response = await requestFn();
    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

// Specific API endpoints
export const apiEndpoints = {
  systemSettings: '/api/v1/system-settings',
  footer: '/api/v1/footer',
  services: '/api/v1/services',
  categories: '/api/v1/categories',
  blogs: '/api/v1/blogs',
  blogDetail: (slug: string) => `/api/v1/blogs/${slug}`,
} as const;


// [
//   {
//       "id": 1,
//       "key": "HERO_BANNER_VIDEO_URL",
//       "value": "https://dl187.dmate9.online/?file=M3R4SUNiN3JsOHJ6WWQ2a3NQS1Y5ZGlxVlZIOCtyaGh2STEvNVVObUpmSUovdFZxODd6bEFwNEtDdjlLN0ttZFFvZ0U1WERWWVlUTk5nYUZzNmMyUTJlSnFwTnZueWJBNXBvMVc4dzBCREQxaTdMbXhCZDN6RlhUWjR2OEdPOEdmMXRXbDFOY2dIREQzTC9acVVHK3RIaWkrbWlrS2lFU29qSVpPT0haL0pvVmhENmVQNk85aDhGUStHQ002NEJNaDZITTdGT2dsT040cThvb1drWWk%3D"
//   },
//   {
//       "id": 2,
//       "key": "HERO_BANNER_HEADLINE",
//       "value": "Elegant pure jewelery with quality materials"
//   },
//   {
//       "id": 3,
//       "key": "HERO_BANNER_DESCRIPTION",
//       "value": "when an unknown printer took a galley of type and scramble it to make a type specimen book. It has survived not only centuries, but also the leap into electronic"
//   },
//   {
//       "id": 4,
//       "key": "HERO_BANNER_CTA_TEXT",
//       "value": "Get Started"
//   },
//   {
//       "id": 5,
//       "key": "HERO_BANNER_CTA_URL",
//       "value": "https://www.instagram.com/swag.gold24/"
//   },
//   {
//       "id": 6,
//       "key": "WHAT_WE_OFFER_CAPTION",
//       "value": "Services"
//   },
//   {
//       "id": 7,
//       "key": "WHAT_WE_OFFER_HEADLINE",
//       "value": "What We Offer"
//   },
//   {
//       "id": 8,
//       "key": "WHAT_WE_OFFER_RICH_TEXT",
//       "value": "Trusted solutions in trading, bullion, logistics, and jewelry."
//   },
//   {
//       "id": 9,
//       "key": "WHAT_WE_OFFER_CTA_TEXT",
//       "value": "Show More Products"
//   },
//   {
//       "id": 10,
//       "key": "WHAT_WE_OFFER_CTA_URL",
//       "value": "https://www.instagram.com/swag.gold24/"
//   },
//   {
//       "id": 11,
//       "key": "PROMOTION_BANNER_LIST_TEXT",
//       "value": [
//           "Immerse yourself in the beauty of our exquisite jewelry, designed with exceptional materials. Seize the opportunity with our exclusive 30% off sale!",
//           "Embellish your style with our sophisticated jewelry collection, showcasing top-tier materials. Take advantage of a fabulous 30% discount on all items!",
//           "Discover the allure of our refined jewelry, expertly crafted from quality materials. Shop now and enjoy a 30% discount on your purchase!",
//           "Reveal the charm of our elegant jewelry line, crafted with the finest materials. Claim your 30% discount today!"
//       ]
//   },
//   {
//       "id": 12,
//       "key": "ABOUT_IMAGE_URL",
//       "value": "https://admin.swaggold.co/storage/images/systemsettings/CHzCABDVluDxzA4e7mTFdChLgv8br3Ni2cP5Erz9.jpg"
//   },
//   {
//       "id": 13,
//       "key": "ABOUT_HEADLINE",
//       "value": "Discover the best jewelry in the world"
//   },
//   {
//       "id": 14,
//       "key": "ABOUT_DESCRIPTION",
//       "value": "SWAG has grown to become one of the leading bullion trading companies in the UAE. Founded in 2013 after a key meeting with Mr. Salem Abdullah Al Ammari, the company was built on trust, ambition, and faith in God. We are grateful for our rapid success and pray it continues.\r\n\r\nMy journey began in a family deeply rooted in the gold and silver trade. From childhood, I learned the craft alongside my father. In 1986, I launched my own wholesale business in Al Yamamah Market, laying the foundation for what SWAG is today."
//   },
//   {
//       "id": 15,
//       "key": "ABOUT_CTA_URL",
//       "value": "https://www.instagram.com/swag.gold24/"
//   },
//   {
//       "id": 16,
//       "key": "IMAGES_SECTION_LIST",
//       "value": [
//           "https://admin.swaggold.co/storage/images/systemsettings/VLqEKYoINeZHoaci5hyUDw7U4EnLzG0nkuFyUHdw.jpg"
//       ]
//   },
//   {
//       "id": 17,
//       "key": "LAST_BLOGS_CAPTION",
//       "value": "Blog"
//   },
//   {
//       "id": 18,
//       "key": "LAST_BLOGS_HEADLINE",
//       "value": "Latest Blogs"
//   },
//   {
//       "id": 19,
//       "key": "LAST_BLOGS_RICH_TEXT",
//       "value": "Articles that provide education to customers discuss diamonds, jewelry, investment, and modern lifestyle"
//   },
//   {
//       "id": 20,
//       "key": "SHOP_NOW_HEADLINE",
//       "value": "Let's keep the collection of jewelry up to date"
//   },
//   {
//       "id": 21,
//       "key": "SHOP_NOW_CTA_TEXT",
//       "value": "Shop Now"
//   },
//   {
//       "id": 22,
//       "key": "SHOP_NOW_CTA_URL",
//       "value": "https://www.instagram.com/swag.gold24/"
//   },
//   {
//       "id": 23,
//       "key": "SHOP_NOW_PROMOTION_IMAGES",
//       "value": [
//           "https://admin.swaggold.co/storage/images/systemsettings/qi67HVaPbIePNHvesgGwZGl28BoCf5rsPwWfvA9W.jpg",
//           "https://admin.swaggold.co/storage/images/systemsettings/5nBlVyiYFo4pAqFFPbTlJ2MpzzOLccq8qZg4yQrN.jpg",
//           "https://admin.swaggold.co/storage/images/systemsettings/giX7Qxl4wWDkPy9v2AznolkP4XEtT9ksFzaRZy8s.jpg",
//           "https://admin.swaggold.co/storage/images/systemsettings/3UcyxUVmgPyLNIvDIQxnC3xaOrgfYDTsxwbdSpHb.jpg",
//           "https://admin.swaggold.co/storage/images/systemsettings/rGCPghvEw0LukfS6aOWpRM8Ihiqfgda3dGgUV4o9.jpg",
//           "https://admin.swaggold.co/storage/images/systemsettings/eorR7niGRXB3S2pikuEdtRAPy3D6tFBeZqUSQdVl.jpg"
//       ]
//   },
//   {
//       "id": 24,
//       "key": "CATEGORY_SECTION_CAPTION",
//       "value": "Attractve jewellery"
//   },
//   {
//       "id": 25,
//       "key": "CATEGORY_SECTION_HEADLINE",
//       "value": "Gorgeous Collections"
//   },
//   {
//       "id": 26,
//       "key": "HEAD_SECTION_MENU_1",
//       "value": "Home"
//   },
//   {
//       "id": 27,
//       "key": "HEAD_SECTION_MENU_2",
//       "value": "About"
//   },
//   {
//       "id": 28,
//       "key": "HEAD_SECTION_MENU_3",
//       "value": "Services"
//   },
//   {
//       "id": 29,
//       "key": "HEAD_SECTION_MENU_4",
//       "value": "Market Insights"
//   }
// ]