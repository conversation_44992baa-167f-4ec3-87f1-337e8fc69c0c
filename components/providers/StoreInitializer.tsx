'use client';

import { useEffect } from 'react';
import { useInitializeStores } from '@/stores';

/**
 * Component to initialize all API stores
 * Should be placed in the root layout to ensure data is loaded early
 */
export default function StoreInitializer() {
  const { initializeAll } = useInitializeStores();

  useEffect(() => {
    // Initialize all stores when the app loads
    initializeAll();
  }, [initializeAll]);

  // This component doesn't render anything
  return null;
}
