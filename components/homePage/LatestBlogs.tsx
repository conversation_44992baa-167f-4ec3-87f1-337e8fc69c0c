"use client"

import React, { useEffect, useRef } from "react"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import BlogCard from "../common/BlogCard"
import { useTranslation } from "react-i18next"

// Register ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger)

interface BlogPost {
  id: string
  date: string
  title: string
  description: string
  image: string
  category: string
  readTime: string
}

const LatestBlogs: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null)
  const titleRef = useRef<HTMLDivElement>(null)
  const blogRefs = useRef<(HTMLDivElement | null)[]>([])
  const { t } = useTranslation()

  const blogPosts: BlogPost[] = [
    {
      id: "1",
      date: "24 Aug 2023",
      title: t("latest_blogs.blog_1.title"),

      description: t("latest_blogs.blog_1.description"),
      image: "/images/blog/gold-coins.jpg",
      category: t("latest_blogs.blog_1.category"),
      readTime: "5 min read"
    },
    {
      id: "2",
      date: "18 Aug 2023",
      title: t("latest_blogs.blog_2.title"),
      description: t("latest_blogs.blog_2.description"),
      image: "/images/blog/diamond-quality.png",
      category: t("latest_blogs.blog_2.category"),
      readTime: "7 min read"
    },
    // Add more blog posts as needed
    {
      id: "3",
      date: "18 Aug 2023",
      title: t("latest_blogs.blog_3.title"),
      description: t("latest_blogs.blog_3.description"),
      image: "/images/blog/diamond-quality.png",
      category: t("latest_blogs.blog_3.category"),
      readTime: "7 min read"
    }
  ]
  useEffect(() => {
    const direction =
      typeof document !== "undefined" && document.documentElement.dir === "rtl"
        ? "rtl"
        : "ltr"

    const ctx = gsap.context(() => {
      gsap.fromTo(
        titleRef.current,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power2.out",
          scrollTrigger: {
            trigger: titleRef.current,
            start: "top 80%",
            end: "bottom 10%",
            toggleActions: "play none none reverse"
          }
        }
      )

      const container = blogRefs.current[0]
      if (!container) return

      const slideDistance = (blogPosts.length) * 100
      const directionMultiplier = direction === "rtl" ? 1 : -1

      gsap.to(container, {
        x: `${directionMultiplier * slideDistance}vw`,
        ease: "none",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top top",
          end: `+=${blogPosts.length * window.innerWidth}`,
          scrub: 1,
          pin: true,
          anticipatePin: 1,
          invalidateOnRefresh: true
        }
      })
    }, sectionRef)

    return () => ctx.revert()
  }, [blogPosts.length])

  return (
    <section
      ref={sectionRef}
      className="py-8 lg:py-24 bg-white dark:bg-secondary-600 transition-colors duration-300 relative"
      style={{
        willChange: "transform",
        transform: "translateZ(0)"
      }}
    >
      <div className="container mx-auto">
        {/* Header */}
        <div ref={titleRef} className="text-center mb-16">
          <p className="text-primary-500  text-lg  mb-4  uppercase">
            {t("latest_blogs.section_title")}
          </p>
          <h2 className="text-4xl lg:text-6xl font-thin text-secondary-500 dark:text-white-50 mb-6">
            {t("latest_blogs.title")}
          </h2>
          <p className="text-md text-secondary-400 dark:text-white-200  mx-auto">
            {t("latest_blogs.subtitle")}
          </p>
        </div>

        {/* Blog Posts */}
        <div className="relative overflow-hidden">
          <div
            className="flex"
            ref={(el) => {
              blogRefs.current[0] = el
            }}
            style={{ width: `${blogPosts.length * 100}vw` }}
          >
            {blogPosts.map((post) => (
              <BlogCard key={post.id} post={post} />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default LatestBlogs
