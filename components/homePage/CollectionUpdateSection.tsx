"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Icon } from "@/components/common/Icon"
import { useTranslation } from "react-i18next"
import { ImageSlider } from "../common/ImageSlider"
import { useLanguageStore } from "@/stores/languageStore"
import { cn } from "@/lib/utils"
import { useSystemSettingsStore } from "@/stores"
import { useRouter } from "next/navigation"

export function CollectionUpdateSection() {
  const { t } = useTranslation()
  const router = useRouter()
  const { isRTL } = useLanguageStore()
  const { getSettingValue } = useSystemSettingsStore()

  const handleShopNow = () => {
    // Add navigation logic here
    router.push(getSettingValue("SHOP_NOW_CTA_URL"))

  }
  const cursorImages = getSettingValue("SHOP_NOW_PROMOTION_IMAGES").map(
    (image: string) => {
      return {
        src: image,
        alt: "Elegant jewelry collection showcase"
      }
    }
  )

  return (
    <section className=" bg-primary-50 dark:bg-primary-100 relative overflow-hidden">
      {/* Sun Icon - Top Left Corner */}
      <div
        className={cn(
          "absolute -top-8  z-10",
          isRTL ? "right-3 rotate-[-20deg]" : "left-3 rotate-[221deg]"
        )}
      >
        <Icon
          name="sun-icon"
          size={32}
          className="text-primary-600 dark:text-primary-200 rotate-45"
        />
      </div>

      <div className="flex flex-col-reverse md:flex-row justify-between gap-12 lg:gap-16 items-end">
        {/* Left Content */}
        <div className="space-y-8  justify-end flex flex-col items-center md:mx-[6rem] mb-14  ">
          <div className="mx-4">
            {/* Main Heading */}
            <h2 className="text-4x max-w-2xl lg:text-5xl  text-primary-600 ">
              {getSettingValue("SHOP_NOW_HEADLINE")}
            </h2>

            {/* Shop Now Button */}
            <div className="pt-4 self-start">
              <Button
                onClick={handleShopNow}
                className="bg-secondary-500 
              text-white-50 px-8 py-4 text-lg font-semibold 
              transition-all duration-300 hover:scale-104 rounded-none min-h-[56px]
              flex items-center gap-3 group"
              >
                {getSettingValue("SHOP_NOW_CTA_TEXT")}
              </Button>
            </div>
          </div>
        </div>

        <div className=" w-full md:max-w-[479px] max-h-[403px] ">
          <ImageSlider
            images={cursorImages}
            autoplay={true}
            autoplayDelay={5000}
            noArrows
            noCounter
            noOverlay
            classImage="w-full md:max-w-[479px] max-h-[403px] h-full object-cover"
          />
          {/* <img
            src="/images/collection-update/jewelry-showcase.jpg"
            alt="Latest jewelry collection showcase"
            className="w-full md:max-w-[479px] max-h-[403px] 
            
            
            h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.style.display = "none"
            }}
          /> */}
        </div>
      </div>
    </section>
  )
}
