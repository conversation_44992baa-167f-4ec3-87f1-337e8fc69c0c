'use client';

import { useEffect } from 'react';
import { useCurrentBlog, useBlogStore, useBlogLoading } from '@/stores/blogStore';

interface BlogDetailPageProps {
  blogSlug: string;
}

export default function BlogDetailPage({ blogSlug }: BlogDetailPageProps) {
  const currentBlog = useCurrentBlog();
  const loading = useBlogLoading();
  const { fetchBlogBySlug } = useBlogStore();

  useEffect(() => {
    fetchBlogBySlug(blogSlug);
  }, [blogSlug, fetchBlogBySlug]);

  if (loading) {
    return <div className="loading">Loading blog...</div>;
  }

  if (!currentBlog) {
    return <div className="error">Blog not found</div>;
  }

  return (
    <article className="container max-w-4xl mx-auto">
      {currentBlog.featured_image && (
        <img 
          src={currentBlog.featured_image} 
          alt={currentBlog.title}
          className="w-full h-64 md:h-96 object-cover rounded-lg mb-8"
        />
      )}
      
      <header className="mb-8">
        <h1 className="text-4xl font-bold mb-4">{currentBlog.title}</h1>
        <div className="flex items-center gap-4 text-gray-600">
          <span>By {currentBlog.author.name}</span>
          <span>•</span>
          <span>{new Date(currentBlog.published_at).toLocaleDateString()}</span>
          <span>•</span>
          <span>{currentBlog.reading_time} min read</span>
        </div>
        <div className="flex items-center gap-2 mt-4">
          <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
            {currentBlog.category.name}
          </span>
          {currentBlog.tags.map((tag) => (
            <span key={tag.id} className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
              {tag.name}
            </span>
          ))}
        </div>
      </header>

      <div 
        className="prose prose-lg max-w-none"
        dangerouslySetInnerHTML={{ __html: currentBlog.content }}
      />
    </article>
  );
}
