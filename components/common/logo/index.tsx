import React, { useState } from "react"
import Image from "next/image"
import { useTheme } from "@/stores/themeStore"

interface LogoProps {
  width?: number
  height?: number
  className?: string
  priority?: boolean
  onClick?: () => void
  white?: boolean
}

export default function Logo({
  width = 120,
  height = 40,
  className = "",
  priority = false,
  white = false,
  onClick
}: LogoProps) {
  const { mode } = useTheme()
  const [imageError, setImageError] = useState(false)

  // Determine which logo to use based on theme
  const logoSrc = !white
    ? "/images/logo-swag-dark.png"
    : "/images/swag-logo-white.png"
  const logoAlt = "Jewelry Premium Logo"
  

  // Fallback logo component when images are not available
  const FallbackLogo = () => (
    <div className="flex items-center space-x-2">
      <div
        className={`w-8 h-8 rounded-lg flex items-center justify-center transition-colors duration-300 ${
          mode === "dark"
            ? "bg-gradient-to-br from-amber-400 to-amber-600"
            : "bg-gradient-to-br from-amber-600 to-amber-800"
        }`}
      >
        <span className="text-white font-bold text-lg">J</span>
      </div>
      <div className="flex flex-col">
        <h1
          className={`text-lg font-bold leading-tight transition-colors duration-300 ${
            mode === "dark" ? "text-white" : "text-gray-900"
          }`}
        >
          Jewelry
        </h1>
        <span
          className={`text-xs font-medium leading-tight transition-colors duration-300 ${
            mode === "dark" ? "text-amber-300" : "text-amber-700"
          }`}
        >
          Premium
        </span>
      </div>
    </div>
  )

  const handleClick = () => {
    if (onClick) {
      onClick()
    }
  }

  const handleImageError = () => {
    setImageError(true)
    console.warn(`Logo image failed to load: ${logoSrc}`)
  }

  const handleImageLoad = () => {
    setImageError(false)
  }

  return (
    <div
      className={`relative flex items-center ${
        onClick ? "cursor-pointer" : ""
      } ${className}`}
      onClick={handleClick}
    >
      {!imageError ? (
        <Image
          src={logoSrc}
          alt={logoAlt}
          width={width}
          height={height}
          priority={priority}
          className="transition-opacity duration-300"
          onError={handleImageError}
          onLoad={handleImageLoad}
        />
      ) : (
        <FallbackLogo />
      )}
    </div>
  )
}
