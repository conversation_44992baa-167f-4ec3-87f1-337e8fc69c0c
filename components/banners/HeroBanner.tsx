'use client';

import { useEffect } from 'react';
import { useHeroBannerData, useBannerStore, useBannerLoading } from '@/stores/bannerStore';

export default function HeroBanner() {
  const heroBannerData = useHeroBannerData();
  const loading = useBannerLoading();
  const { fetchBannerSettings } = useBannerStore();

  useEffect(() => {
    // Only fetch when component is used
    fetchBannerSettings();
  }, [fetchBannerSettings]);

  if (loading) {
    return <div className="hero-banner-loading">Loading banner...</div>;
  }

  return (
    <section className="hero-banner relative w-full h-screen overflow-hidden">
      {heroBannerData.videoUrl && (
        <video
          className="absolute inset-0 w-full h-full object-cover"
          src={heroBannerData.videoUrl}
          autoPlay
          muted
          loop
        />
      )}
      
      <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
        <div className="text-center text-white max-w-4xl px-6">
          {heroBannerData.headline && (
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {heroBannerData.headline}
            </h1>
          )}
          
          {heroBannerData.description && (
            <p className="text-lg md:text-xl mb-8 max-w-2xl mx-auto">
              {heroBannerData.description}
            </p>
          )}
          
          {heroBannerData.ctaText && heroBannerData.ctaUrl && (
            <a
              href={heroBannerData.ctaUrl}
              className="inline-block bg-yellow-500 hover:bg-yellow-600 text-black font-semibold px-8 py-3 rounded-lg transition-colors duration-300"
            >
              {heroBannerData.ctaText}
            </a>
          )}
        </div>
      </div>
    </section>
  );
}
