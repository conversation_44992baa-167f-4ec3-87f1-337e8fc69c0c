'use client';

import { useEffect, useState } from 'react';
import { usePromotionBannerData, useBannerStore } from '@/stores/bannerStore';

export default function PromotionBanner() {
  const promotionBannerData = usePromotionBannerData();
  const { fetchBannerSettings } = useBannerStore();
  const [currentTextIndex, setCurrentTextIndex] = useState(0);

  useEffect(() => {
    // Only fetch when component is used
    fetchBannerSettings();
  }, [fetchBannerSettings]);

  useEffect(() => {
    if (promotionBannerData.listText && promotionBannerData.listText.length > 1) {
      const interval = setInterval(() => {
        setCurrentTextIndex((prev) => 
          (prev + 1) % promotionBannerData.listText!.length
        );
      }, 4000); // Change text every 4 seconds

      return () => clearInterval(interval);
    }
  }, [promotionBannerData.listText]);

  if (!promotionBannerData.listText || promotionBannerData.listText.length === 0) {
    return null;
  }

  return (
    <div className="promotion-banner bg-gradient-to-r from-yellow-400 to-yellow-600 py-3 overflow-hidden">
      <div className="container mx-auto">
        <div className="flex items-center justify-center">
          <div className="text-center text-black font-medium">
            <div className="animate-fade-in-out">
              {promotionBannerData.listText[currentTextIndex]}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
