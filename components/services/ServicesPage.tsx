'use client';

import { useActiveServices } from '@/stores/servicesStore';

export default function ServicesPage() {
  const services = useActiveServices();

  return (
    <div className="services-page">
      <h1 className="text-3xl font-bold mb-8">Our Services</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {services.map((service) => (
          <div key={service.id} className="service-item border rounded-lg p-6">
            {service.image && (
              <img 
                src={service.image} 
                alt={service.title}
                className="w-full h-48 object-cover rounded-lg mb-4"
              />
            )}
            <h2 className="text-2xl font-semibold mb-4">{service.title}</h2>
            <p className="text-gray-600 mb-4">{service.description}</p>
            {service.icon && (
              <div className="flex items-center gap-2 text-blue-600">
                <span>{service.icon}</span>
                <span>Learn More</span>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
