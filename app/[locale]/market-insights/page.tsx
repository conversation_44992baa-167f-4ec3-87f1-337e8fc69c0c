import { notFound } from 'next/navigation';
import MarketInsightsPage from '@/components/marketInsights/MarketInsightsPage';

interface MarketInsightsPageProps {
  params: {
    locale: string;
  };
}

// Define supported locales
const supportedLocales = ['en', 'ar'];

export default function LocalizedMarketInsightsPage({ params }: MarketInsightsPageProps) {
  // Validate locale
  if (!supportedLocales.includes(params.locale)) {
    notFound();
  }

  return <MarketInsightsPage />;
}

// Generate static params for supported locales
export async function generateStaticParams() {
  return supportedLocales.map((locale) => ({
    locale,
  }));
}
