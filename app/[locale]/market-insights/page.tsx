import MarketInsights from '@/components/marketInsights';
import { notFound } from 'next/navigation';

interface MarketInsightsPageProps {
  params: {
    locale: string;
  };
}

// Define supported locales
const supportedLocales = ['en', 'ar'];

export default function LocalizedMarketInsightsPage({ params }: MarketInsightsPageProps) {
  // Validate locale
  if (!supportedLocales.includes(params.locale)) {
    notFound();
  }

  return <MarketInsights />;
}

// Generate static params for supported locales
export async function generateStaticParams() {
  return supportedLocales.map((locale) => ({
    locale,
  }));
}
