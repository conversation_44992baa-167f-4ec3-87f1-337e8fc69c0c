import { MainLayout } from '@/components/layout/MainLayout';
import { notFound } from 'next/navigation';
import { ReactNode } from 'react';

interface MarketInsightsLayoutProps {
  children: ReactNode;
  params: {
    locale: string;
  };
}

// Define supported locales
const supportedLocales = ['en', 'ar'];

export default function LocalizedMarketInsightsLayout({ children, params }: MarketInsightsLayoutProps) {
  // Validate locale
  if (!supportedLocales.includes(params.locale)) {
    notFound();
  }

  return (
    <MainLayout>
      {children}
    </MainLayout>
  );
}

// Generate static params for supported locales
export async function generateStaticParams() {
  return supportedLocales.map((locale) => ({
    locale,
  }));
}
