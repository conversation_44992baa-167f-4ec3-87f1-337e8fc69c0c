import { notFound } from 'next/navigation';
import { ReactNode } from 'react';

interface LocaleLayoutProps {
  children: ReactNode;
  params: {
    locale: string;
  };
}

// Define supported locales
const supportedLocales = ['en', 'ar'];

export default function LocaleLayout({ children, params }: LocaleLayoutProps) {
  // Validate locale
  if (!supportedLocales.includes(params.locale)) {
    notFound();
  }

  return (
    <div className={`locale-${params.locale}`}>
      {children}
    </div>
  );
}

// Generate static params for supported locales
export async function generateStaticParams() {
  return supportedLocales.map((locale) => ({
    locale,
  }));
}
