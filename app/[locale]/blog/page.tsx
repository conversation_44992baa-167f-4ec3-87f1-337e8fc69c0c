import { notFound } from 'next/navigation';
import BlogDetailPage from '@/components/blogDetails';

interface BlogPageProps {
  params: {
    locale: string;
  };
}

// Define supported locales
const supportedLocales = ['en', 'ar'];

export default function LocalizedBlogPage({ params }: BlogPageProps) {
  // Validate locale
  if (!supportedLocales.includes(params.locale)) {
    notFound();
  }

  return <BlogDetailPage blogSlug={params.locale} />;
}

// Generate static params for supported locales
export async function generateStaticParams() {
  return supportedLocales.map((locale) => ({
    locale,
  }));
}
