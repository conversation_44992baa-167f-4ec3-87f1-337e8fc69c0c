import HomePage from "@/components/homePage"
import { MainLayout } from "@/components/layout/MainLayout"
import { notFound } from "next/navigation"

interface HomePageProps {
  params: {
    locale: string
  }
}

// Define supported locales
const supportedLocales = ["en", "ar"]

export default function LocalizedHomePage({ params }: HomePageProps) {
  // Validate locale
  if (!supportedLocales.includes(params.locale)) {
    notFound()
  }

  return (
    <MainLayout>
      <HomePage />
    </MainLayout>
  )
}

// Generate static params for supported locales
export async function generateStaticParams() {
  return supportedLocales.map((locale) => ({
    locale
  }))
}
